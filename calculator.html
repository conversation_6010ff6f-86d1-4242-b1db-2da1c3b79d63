<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Calculator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .calculator {
            background: #2c3e50;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 350px;
            width: 100%;
        }

        .display {
            background: #34495e;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: right;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .display-text {
            color: #ecf0f1;
            font-size: 2.5rem;
            font-weight: 300;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .btn {
            border: none;
            border-radius: 10px;
            font-size: 1.2rem;
            font-weight: 600;
            height: 60px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .btn-number {
            background: #95a5a6;
            color: #2c3e50;
        }

        .btn-number:hover {
            background: #bdc3c7;
        }

        .btn-operator {
            background: #e74c3c;
            color: white;
        }

        .btn-operator:hover {
            background: #c0392b;
        }

        .btn-equals {
            background: #27ae60;
            color: white;
        }

        .btn-equals:hover {
            background: #229954;
        }

        .btn-clear {
            background: #f39c12;
            color: white;
        }

        .btn-clear:hover {
            background: #e67e22;
        }

        .btn-zero {
            grid-column: span 2;
        }
    </style>
</head>
<body>
    <div class="calculator">
        <div class="display">
            <div class="display-text" id="display">0</div>
        </div>
        <div class="buttons">
            <button class="btn btn-clear" onclick="clearDisplay()">C</button>
            <button class="btn btn-operator" onclick="appendToDisplay('/')">/</button>
            <button class="btn btn-operator" onclick="appendToDisplay('*')">×</button>
            <button class="btn btn-operator" onclick="deleteLast()">⌫</button>
            
            <button class="btn btn-number" onclick="appendToDisplay('7')">7</button>
            <button class="btn btn-number" onclick="appendToDisplay('8')">8</button>
            <button class="btn btn-number" onclick="appendToDisplay('9')">9</button>
            <button class="btn btn-operator" onclick="appendToDisplay('-')">-</button>
            
            <button class="btn btn-number" onclick="appendToDisplay('4')">4</button>
            <button class="btn btn-number" onclick="appendToDisplay('5')">5</button>
            <button class="btn btn-number" onclick="appendToDisplay('6')">6</button>
            <button class="btn btn-operator" onclick="appendToDisplay('+')">+</button>
            
            <button class="btn btn-number" onclick="appendToDisplay('1')">1</button>
            <button class="btn btn-number" onclick="appendToDisplay('2')">2</button>
            <button class="btn btn-number" onclick="appendToDisplay('3')">3</button>
            <button class="btn btn-equals" onclick="calculate()" rowspan="2">=</button>
            
            <button class="btn btn-number btn-zero" onclick="appendToDisplay('0')">0</button>
            <button class="btn btn-number" onclick="appendToDisplay('.')">.</button>
        </div>
    </div>

    <script>
        let display = document.getElementById('display');
        let currentInput = '0';
        let shouldResetDisplay = false;

        function updateDisplay() {
            display.textContent = currentInput;
        }

        function appendToDisplay(value) {
            if (shouldResetDisplay) {
                currentInput = '';
                shouldResetDisplay = false;
            }
            
            if (currentInput === '0' && value !== '.') {
                currentInput = value;
            } else {
                currentInput += value;
            }
            
            updateDisplay();
        }

        function clearDisplay() {
            currentInput = '0';
            updateDisplay();
        }

        function deleteLast() {
            if (currentInput.length > 1) {
                currentInput = currentInput.slice(0, -1);
            } else {
                currentInput = '0';
            }
            updateDisplay();
        }

        function calculate() {
            try {
                // Replace × with * for evaluation
                let expression = currentInput.replace(/×/g, '*');
                
                // Evaluate the expression
                let result = eval(expression);
                
                // Handle division by zero and other edge cases
                if (!isFinite(result)) {
                    currentInput = 'Error';
                } else {
                    currentInput = result.toString();
                }
                
                shouldResetDisplay = true;
                updateDisplay();
            } catch (error) {
                currentInput = 'Error';
                shouldResetDisplay = true;
                updateDisplay();
            }
        }

        // Keyboard support
        document.addEventListener('keydown', function(event) {
            const key = event.key;
            
            if (key >= '0' && key <= '9') {
                appendToDisplay(key);
            } else if (key === '.') {
                appendToDisplay('.');
            } else if (key === '+' || key === '-') {
                appendToDisplay(key);
            } else if (key === '*') {
                appendToDisplay('*');
            } else if (key === '/') {
                event.preventDefault();
                appendToDisplay('/');
            } else if (key === 'Enter' || key === '=') {
                event.preventDefault();
                calculate();
            } else if (key === 'Escape' || key === 'c' || key === 'C') {
                clearDisplay();
            } else if (key === 'Backspace') {
                event.preventDefault();
                deleteLast();
            }
        });
    </script>
</body>
</html>
